package main

import (
	"context"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

func main() {
	// Test the new UserTaskListByCategory functionality
	fmt.Println("Testing UserTaskListByCategory functionality...")

	// Create a resolver
	resolver := resolvers.NewActivityCashbackResolver()

	// Create a mock context with user ID
	userID := uuid.New()
	ctx := context.WithValue(context.Background(), "userId", userID.String())

	// Test input
	input := gql_model.UserTaskListByCategoryInput{
		CategoryName: "daily",
	}

	// Call the resolver
	response, err := resolver.UserTaskListByCategory(ctx, input)
	if err != nil {
		log.Printf("Error calling UserTaskListByCategory: %v", err)
		return
	}

	// Print results
	fmt.Printf("Success: %v\n", response.Success)
	fmt.Printf("Message: %s\n", response.Message)
	fmt.Printf("Number of tasks: %d\n", len(response.Data))

	for i, taskWithProgress := range response.Data {
		fmt.Printf("Task %d: %s\n", i+1, taskWithProgress.Task.Name)
		if taskWithProgress.Progress != nil {
			fmt.Printf("  Status: %v\n", taskWithProgress.Progress.Status)
			fmt.Printf("  Progress: %d\n", taskWithProgress.Progress.ProgressValue)
		} else {
			fmt.Printf("  No progress data\n")
		}
	}

	fmt.Println("Test completed successfully!")
}
